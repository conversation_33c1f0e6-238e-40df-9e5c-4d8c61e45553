import { extractTableFromEChartsOption } from '../echartsToTable';

describe('echartsToTable', () => {
  // 测试柱状图数据提取
  test('should extract table data from bar chart', () => {
    const barChartOption = {
      xAxis: {
        type: 'category',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '销量',
          type: 'bar',
          data: [120, 200, 150, 80, 70]
        }
      ]
    };

    const result = extractTableFromEChartsOption(barChartOption);
    
    expect(result.columns).toHaveLength(2);
    expect(result.columns[0].title).toBe('分类');
    expect(result.columns[1].title).toBe('销量');
    expect(result.dataSource).toHaveLength(5);
    expect(result.dataSource[0].category).toBe('Mon');
    expect(result.dataSource[0].series_0).toBe(120);
  });

  // 测试饼图数据提取
  test('should extract table data from pie chart', () => {
    const pieChartOption = {
      series: [
        {
          name: '访问来源',
          type: 'pie',
          data: [
            { value: 1048, name: '搜索引擎' },
            { value: 735, name: '直接访问' },
            { value: 580, name: '邮件营销' },
            { value: 484, name: '联盟广告' }
          ]
        }
      ]
    };

    const result = extractTableFromEChartsOption(pieChartOption);
    
    expect(result.columns).toHaveLength(2);
    expect(result.columns[0].title).toBe('名称');
    expect(result.columns[1].title).toBe('访问来源');
    expect(result.dataSource).toHaveLength(4);
    expect(result.dataSource[0].name).toBe('搜索引擎');
    expect(result.dataSource[0].value).toBe(1048);
  });

  // 测试空数据情况
  test('should handle empty data gracefully', () => {
    const emptyOption = {};
    const result = extractTableFromEChartsOption(emptyOption);
    
    expect(result.columns).toHaveLength(0);
    expect(result.dataSource).toHaveLength(0);
  });

  // 测试无效数据情况
  test('should handle invalid data gracefully', () => {
    const result1 = extractTableFromEChartsOption(null);
    expect(result1.columns).toHaveLength(0);
    expect(result1.dataSource).toHaveLength(0);

    const result2 = extractTableFromEChartsOption(undefined);
    expect(result2.columns).toHaveLength(0);
    expect(result2.dataSource).toHaveLength(0);
  });

  // 测试多系列数据
  test('should extract table data from multi-series chart', () => {
    const multiSeriesOption = {
      xAxis: {
        type: 'category',
        data: ['Jan', 'Feb', 'Mar']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '销量',
          type: 'bar',
          data: [100, 200, 300]
        },
        {
          name: '利润',
          type: 'line',
          data: [50, 100, 150]
        }
      ]
    };

    const result = extractTableFromEChartsOption(multiSeriesOption);
    
    expect(result.columns).toHaveLength(3); // 分类 + 2个系列
    expect(result.columns[0].title).toBe('分类');
    expect(result.columns[1].title).toBe('销量');
    expect(result.columns[2].title).toBe('利润');
    expect(result.dataSource).toHaveLength(3);
    expect(result.dataSource[0].category).toBe('Jan');
    expect(result.dataSource[0].series_0).toBe(100);
    expect(result.dataSource[0].series_1).toBe(50);
  });
});
