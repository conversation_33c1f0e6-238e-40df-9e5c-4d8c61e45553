import React from 'react';
import { Modal, Table, Button } from 'antd';
import { DownloadOutlined } from '@ant-design/icons';
import * as XLSX from 'xlsx';

interface TableViewModalProps {
  visible: boolean;
  onCancel: () => void;
  title?: string;
  columns: any[];
  dataSource: any[];
}

const TableViewModal: React.FC<TableViewModalProps> = ({
  visible,
  onCancel,
  title = '图表数据表格',
  columns,
  dataSource,
}) => {
  // 导出为 Excel
  const handleExportExcel = () => {
    try {
      // 创建工作簿
      const wb = XLSX.utils.book_new();
      
      // 准备数据：将表格数据转换为二维数组
      const headers = columns.map(col => col.title);
      const data = dataSource.map(row => 
        columns.map(col => row[col.dataIndex] || '')
      );
      
      // 合并表头和数据
      const wsData = [headers, ...data];
      
      // 创建工作表
      const ws = XLSX.utils.aoa_to_sheet(wsData);
      
      // 设置列宽
      const colWidths = columns.map(() => ({ wch: 15 }));
      ws['!cols'] = colWidths;
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '图表数据');
      
      // 生成文件名
      const fileName = `${title}_${new Date().toLocaleDateString()}.xlsx`;
      
      // 导出文件
      XLSX.writeFile(wb, fileName);
    } catch (error) {
      console.error('导出Excel失败:', error);
    }
  };

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={onCancel}
      width="80%"
      footer={[
        <Button key="export" icon={<DownloadOutlined />} onClick={handleExportExcel}>
          导出Excel
        </Button>,
        <Button key="close" onClick={onCancel}>
          关闭
        </Button>,
      ]}
      className="table-view-modal"
    >
      <div style={{ maxHeight: '60vh', overflow: 'auto' }}>
        <Table
          columns={columns}
          dataSource={dataSource}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100'],
            defaultPageSize: 20,
          }}
          scroll={{ x: 'max-content' }}
          size="small"
        />
      </div>
    </Modal>
  );
};

export default TableViewModal;
